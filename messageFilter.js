// 创建过滤器功能
function createMessageFilter(options = {}) {
  return (message) => {
    // 基于内容过滤
    if (options.keywords && options.keywords.length > 0) {
      const messageContent = message.content.toLowerCase();
      if (!options.keywords.some(keyword => messageContent.includes(keyword.toLowerCase()))) {
        return false;
      }
    }
    
    // 基于用户过滤
    if (options.users && options.users.length > 0) {
      if (!options.users.includes(message.author.id)) {
        return false;
      }
    }
    
    return true;
  };
}

module.exports = { createMessageFilter }; 