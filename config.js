module.exports = {
  // Discord 配置
  // token: "MTM1MjgyNDU1MjM5NjI5MjA5Ng.GMTU0b.jIeniY0Biu23qcploOcY2KLiFrYkucmFHOmu-s",
  // 生产环境
  token: "MTM1NTgyMDEzNTY1NDM1OTE3Mw.Gnf0mo.2dJYu2lFH0yo0TvwzGHg6UITw-NWgs-bPK5BVA",
  
  // AI 配置
  ai: {
    apiKey: "sk-100b889c625e437b9b556523f4e2a7bd",
    baseURL: "https://api.deepseek.com/v1",
    model: "deepseek-chat",
    temperature: 1.3,
    maxTokens: 4000
  },
  banWords: ['NOTI GANG', 'NOTICE', 'FREE', 'Platinum'],
  // 频道配置 name 仅方便自己查看 无实际用处
  channels: [
    {
      name: "（news）",
      sourceId: "940255511864033320",
      webhookUrl: "https://discordapp.com/api/webhooks/1356817206775910516/thEwALnxehawNpI2ugBtKxnyKE1FXlmOE-cUbZQwf-zVhJ-_FJujNzARyaGjdaVgzHEO",
      useTranslate: true,
    },
    {
      name: "（社长）",
      sourceId: "1340967745688174633",
      webhookUrl: "https://discordapp.com/api/webhooks/1356812412833104002/9G2n5T2zrScR4-bVvuPoVkDrEjt-uoipaW1sXawdJfrZtJBytl9Hb6GyxaAQK4Sybx9N",
      useTranslate: false,
    },
    {
      name: "稳定lin",
      sourceId: "1340594180208201748",
      webhookUrl: "https://discordapp.com/api/webhooks/1379344683414716436/E3Z1wkUvJ8rdXLdVCE07z4ljA2LYQy4vlvlHOmgERmtSgIv_7PJCtrbiRUpBWt-UV0aZ",
      useTranslate: false,
      onlyText: true,
    },
    {
      name: "enrich",
      sourceId: "1340594343760629771",
      webhookUrl: "https://discordapp.com/api/webhooks/1379345318222757979/SNYhpMhm7_1fafE4HmDrMEBIx34YJFVoFEGL99Clha2EE6PEzt0V83NbTaHcaqh-PAJc",
      useTranslate: false,
      onlyText: false,
    },
    {
      name: "纳指",
      sourceId: "1341807619286765618",
      webhookUrl: "https://discordapp.com/api/webhooks/1379345607914819634/ceu5t78S77loKyUHmBQfSWKiVs49qAHWzmWA5Ry2PGbSGcGQScqw1qsBbDQfOLNivuYx",
      useTranslate: false,
      onlyText: false
    },
    {
      name: "精选mave",
      sourceId: "1356975339875729624",
      webhookUrl: "https://discordapp.com/api/webhooks/1379345828950310953/KCiv3ow8EJKP4wYvWOeY-RDa_1ARes2MekIteCx2ATTJHFbYxBbqQr1seiOmDwtdDuJm",
      useTranslate: true,
    },
    {
      name: "nitro",
      sourceId: "1341808356079439882",
      webhookUrl: "https://discordapp.com/api/webhooks/1379346426202427412/TNKVU1cztjlt64sKX8Kh3P52MQoho306iWGaK2MGR9Gw9Zwi32vxJdQrwm8PsfrFYKwd",
      useTranslate: false,
      onlyText: true,
    },
    {
      name: "remz",
      sourceId: "1376804728247291985",
      webhookUrl: "https://discordapp.com/api/webhooks/1379346722123153549/VXzjas3WafvkgL0n8HVnRUUKzDYk_If_TcySV5fDC2_kOQelc2BBFyqGgIAIPY_YZqXt",
      useTranslate: false,
    },
    {
      name: "长线Christian",
      sourceId: "1379351056299069460",
      webhookUrl: "https://discordapp.com/api/webhooks/1379347343249506356/2twmAcRjXM9zCrDS0nW4xzLCSFL97cVyACT6Me6bZkHTohXX4IUuFNzvVpus2lJ1hYlL",
      useTranslate: false,
    },
    {
      name: "期权异动",
      sourceId: "1379351194320765029",
      webhookUrl: "https://discordapp.com/api/webhooks/1379347580714094623/Yq7A9E5eiN3_O8AgtfKoC8-PzMHhbfwHePLRK5PZTIyYiCEhYSnegung50ezH3S3FlAj",
      useTranslate: false,
    },
    // {
    //   name: "test用",
    //   sourceId: "1352825214941138956",
    //   webhookUrl: "https://discord.com/api/webhooks/1354426200487104543/VCzIuif1V_QwfkKYW_jhsApHuZcP7IjsLZKHYJZYQeWjjdqC_x7LGS8L6LfcZ6eDx_TH",
    //   useTranslate: true,
    //   onlyText: true,
    // },
  ]
};
先帮我出方案 ，按照当前 @/Users/<USER>/work/outsourcing/discord-forward-饭太硬/config.js  配置  增加一个

botToken : '' , tgBanWords: [...] 和 tgChannels: [{sourceId:'', targetId： '' ,useTranslate: true}] 这样配置  在 @/Users/<USER>/work/outsourcing/discord-forward-饭太硬/index.js 的消息监听中 增加一个转发到 telegram channel 的功能 其中配置的 botToken, 就是 telegram 的 bottoken， sourceId 就是discord 消息源的id 跟当前逻辑一样 targetId 就是 telegram 的目标群组 id ; 帮我想一个实现方案